import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import LoadingSpinner from './LoadingSpinner';

interface RealtimeChartProps {
  data: any[];
  isLoading?: boolean;
}

const RealtimeChart: React.FC<RealtimeChartProps> = ({ data, isLoading }) => {
  const [chartData, setChartData] = useState<any[]>([]);

  useEffect(() => {
    // Process data for chart display
    const processedData = data.slice(-20).map((item, index) => ({
      time: new Date(item.date_start || Date.now()).toLocaleTimeString(),
      connections: data.slice(0, index + 1).length,
      timestamp: Date.now() - (20 - index) * 60000, // Mock timestamps
    }));

    setChartData(processedData);
  }, [data]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="time" 
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis 
            stroke="#6b7280"
            fontSize={12}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            }}
          />
          <Line 
            type="monotone" 
            dataKey="connections" 
            stroke="#3b82f6" 
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RealtimeChart;
