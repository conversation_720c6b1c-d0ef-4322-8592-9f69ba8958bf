import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Search, Wifi, WifiOff } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/services';

interface HeaderProps {
  onMenuClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  // Check API connection status
  const { data: isConnected, isLoading } = useQuery({
    queryKey: ['api-connection'],
    queryFn: () => apiClient.testConnection(),
    refetchInterval: 30000, // Check every 30 seconds
    retry: 1,
  });

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
        {/* Left side */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="hidden lg:block">
            <h1 className="text-xl font-semibold text-gray-900">
              XUI API Management Dashboard
            </h1>
            <p className="text-sm text-gray-500">
              Real-time IPTV streaming control and monitoring
            </p>
          </div>
        </div>

        {/* Center - Search */}
        <div className="hidden md:block flex-1 max-w-lg mx-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search users, streams, channels..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-4 w-4 bg-gray-300 rounded-full"></div>
              </div>
            ) : isConnected ? (
              <div className="flex items-center space-x-1 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-xs font-medium">Connected</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-xs font-medium">Disconnected</span>
              </div>
            )}
          </div>

          {/* Notifications */}
          <button className="relative p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-md">
            <Bell className="h-6 w-6" />
            <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
          </button>

          {/* API Info */}
          <div className="hidden sm:block text-right">
            <p className="text-xs text-gray-500">API Endpoint</p>
            <p className="text-xs font-mono text-gray-700">
              {apiClient.getConfig().baseUrl}
            </p>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
