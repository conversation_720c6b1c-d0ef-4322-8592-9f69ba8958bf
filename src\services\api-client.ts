import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiConfig, ApiResponse } from '@/types/api';

export class XUIApiClient {
  private client: AxiosInstance;
  private config: ApiConfig;

  constructor(config: ApiConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add API key and access code
    this.client.interceptors.request.use(
      (config) => {
        // Add API key and access code to all requests
        const params = new URLSearchParams(config.data || '');
        params.append('api_key', this.config.apiKey);
        
        // Build the URL with access code
        const url = `/${this.config.accessCode}/`;
        config.url = url;
        config.data = params.toString();
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        console.error('API Error:', error);
        
        if (error.response) {
          // Server responded with error status
          const message = error.response.data?.message || error.response.statusText;
          throw new Error(`API Error (${error.response.status}): ${message}`);
        } else if (error.request) {
          // Request was made but no response received
          throw new Error('Network Error: No response from server');
        } else {
          // Something else happened
          throw new Error(`Request Error: ${error.message}`);
        }
      }
    );
  }

  // Generic method to make API calls
  async makeRequest<T = any>(action: string, params: Record<string, any> = {}): Promise<ApiResponse<T>> {
    try {
      const requestParams = new URLSearchParams();
      requestParams.append('action', action);
      
      // Add all parameters
      Object.entries(params).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          // Handle arrays (like bouquets_selected[])
          value.forEach((item) => {
            requestParams.append(`${key}[]`, String(item));
          });
        } else if (value !== undefined && value !== null) {
          requestParams.append(key, String(value));
        }
      });

      const response = await this.client.post('', requestParams.toString());
      
      // Handle different response formats
      if (typeof response.data === 'object') {
        return {
          success: true,
          data: response.data,
        };
      } else {
        // Some endpoints might return plain text or other formats
        return {
          success: true,
          data: response.data as T,
        };
      }
    } catch (error) {
      console.error(`API Request failed for action: ${action}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Utility method to handle file uploads
  async uploadFile(action: string, file: File, additionalParams: Record<string, any> = {}): Promise<ApiResponse> {
    try {
      const formData = new FormData();
      formData.append('action', action);
      formData.append('api_key', this.config.apiKey);
      formData.append('file', file);
      
      Object.entries(additionalParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, String(value));
        }
      });

      const response = await this.client.post(`/${this.config.accessCode}/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error(`File upload failed for action: ${action}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  // Method to test API connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('get_settings');
      return response.success;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  // Method to update API configuration
  updateConfig(newConfig: Partial<ApiConfig>) {
    this.config = { ...this.config, ...newConfig };
    
    // Update base URL if changed
    if (newConfig.baseUrl) {
      this.client.defaults.baseURL = newConfig.baseUrl;
    }
  }

  // Get current configuration (without sensitive data)
  getConfig(): Omit<ApiConfig, 'apiKey'> {
    return {
      baseUrl: this.config.baseUrl,
      accessCode: this.config.accessCode,
    };
  }
}

// Default API client instance
export const defaultApiConfig: ApiConfig = {
  baseUrl: 'http://104.243.33.231',
  apiKey: '1AA7A7903032096A174ED700B1048EDC',
  accessCode: '5m3gVTKM',
};

export const apiClient = new XUIApiClient(defaultApiConfig);
