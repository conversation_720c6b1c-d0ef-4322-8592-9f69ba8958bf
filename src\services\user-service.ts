import { XUIApiClient } from './api-client';
import { User, CreateUserRequest, ApiResponse, Line, MagDevice, EnigmaDevice } from '@/types/api';

export class UserService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all users
  async getUsers(): Promise<ApiResponse<User[]>> {
    return this.apiClient.makeRequest<User[]>('get_users');
  }

  // Get specific user by ID
  async getUser(id: number): Promise<ApiResponse<User>> {
    return this.apiClient.makeRequest<User>('get_user', { id });
  }

  // Create new user
  async createUser(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    return this.apiClient.makeRequest<User>('create_user', userData);
  }

  // Edit existing user
  async editUser(id: number, userData: Partial<CreateUserRequest>): Promise<ApiResponse<User>> {
    return this.apiClient.makeRequest<User>('edit_user', { id, ...userData });
  }

  // Delete user
  async deleteUser(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_user', { id });
  }

  // Enable user
  async enableUser(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('enable_user', { id });
  }

  // Disable user
  async disableUser(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('disable_user', { id });
  }

  // Get user info (detailed information)
  async getUserInfo(username?: string): Promise<ApiResponse> {
    const params = username ? { username } : {};
    return this.apiClient.makeRequest('user_info', params);
  }
}

export class LineService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all lines
  async getLines(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_lines');
  }

  // Get specific line by ID
  async getLine(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_line', { id });
  }

  // Create new line
  async createLine(lineData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('create_line', lineData);
  }

  // Edit existing line
  async editLine(id: number, lineData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('edit_line', { id, ...lineData });
  }

  // Delete line
  async deleteLine(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_line', { id });
  }

  // Enable line
  async enableLine(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('enable_line', { id });
  }

  // Disable line
  async disableLine(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('disable_line', { id });
  }

  // Ban line
  async banLine(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('ban_line', { id });
  }

  // Unban line
  async unbanLine(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('unban_line', { id });
  }
}

export class MagService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all MAG devices
  async getMags(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_mags');
  }

  // Get specific MAG device by ID
  async getMag(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_mag', { id });
  }

  // Create new MAG device
  async createMag(magData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('create_mag', magData);
  }

  // Edit existing MAG device
  async editMag(id: number, magData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('edit_mag', { id, ...magData });
  }

  // Delete MAG device
  async deleteMag(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_mag', { id });
  }

  // Enable MAG device
  async enableMag(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('enable_mag', { id });
  }

  // Disable MAG device
  async disableMag(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('disable_mag', { id });
  }

  // Ban MAG device
  async banMag(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('ban_mag', { id });
  }

  // Unban MAG device
  async unbanMag(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('unban_mag', { id });
  }

  // Convert MAG device
  async convertMag(id: number, convertData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('convert_mag', { id, ...convertData });
  }
}

export class EnigmaService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all Enigma devices
  async getEnigmas(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_enigmas');
  }

  // Get specific Enigma device by ID
  async getEnigma(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_enigma', { id });
  }

  // Create new Enigma device
  async createEnigma(enigmaData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('create_enigma', enigmaData);
  }

  // Edit existing Enigma device
  async editEnigma(id: number, enigmaData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('edit_enigma', { id, ...enigmaData });
  }

  // Delete Enigma device
  async deleteEnigma(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_enigma', { id });
  }

  // Enable Enigma device
  async enableEnigma(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('enable_enigma', { id });
  }

  // Disable Enigma device
  async disableEnigma(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('disable_enigma', { id });
  }

  // Ban Enigma device
  async banEnigma(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('ban_enigma', { id });
  }

  // Unban Enigma device
  async unbanEnigma(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('unban_enigma', { id });
  }

  // Convert Enigma device
  async convertEnigma(id: number, convertData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('convert_enigma', { id, ...convertData });
  }
}
