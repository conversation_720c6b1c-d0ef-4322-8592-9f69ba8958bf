import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Users, 
  Tv, 
  Radio, 
  Film, 
  Activity, 
  Server, 
  TrendingUp, 
  AlertTriangle,
  Eye,
  Download,
  Upload
} from 'lucide-react';
import { 
  userService, 
  streamService, 
  channelService, 
  movieService, 
  logsService, 
  monitoringService 
} from '@/services';
import MetricCard from '@/components/ui/MetricCard';
import RealtimeChart from '@/components/ui/RealtimeChart';
import ActivityFeed from '@/components/ui/ActivityFeed';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

const Dashboard: React.FC = () => {
  // Fetch dashboard data
  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ['users'],
    queryFn: () => userService.getUsers(),
    refetchInterval: 30000,
  });

  const { data: streams, isLoading: streamsLoading } = useQuery({
    queryKey: ['streams'],
    queryFn: () => streamService.getStreams(),
    refetchInterval: 30000,
  });

  const { data: channels, isLoading: channelsLoading } = useQuery({
    queryKey: ['channels'],
    queryFn: () => channelService.getChannels(),
    refetchInterval: 30000,
  });

  const { data: movies, isLoading: moviesLoading } = useQuery({
    queryKey: ['movies'],
    queryFn: () => movieService.getMovies(),
    refetchInterval: 30000,
  });

  const { data: liveConnections, isLoading: connectionsLoading } = useQuery({
    queryKey: ['live-connections'],
    queryFn: () => logsService.getLiveConnections(),
    refetchInterval: 5000, // Update every 5 seconds for real-time
  });

  const { data: serverStats, isLoading: statsLoading } = useQuery({
    queryKey: ['server-stats'],
    queryFn: () => monitoringService.getServerStats(),
    refetchInterval: 10000, // Update every 10 seconds
  });

  const { data: activityLogs, isLoading: logsLoading } = useQuery({
    queryKey: ['activity-logs'],
    queryFn: () => logsService.getActivityLogs({ limit: 10 }),
    refetchInterval: 15000,
  });

  const isLoading = usersLoading || streamsLoading || channelsLoading || moviesLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  // Calculate metrics
  const totalUsers = users?.data?.length || 0;
  const activeUsers = users?.data?.filter((user: any) => user.enabled)?.length || 0;
  const totalStreams = streams?.data?.length || 0;
  const activeStreams = streams?.data?.filter((stream: any) => stream.enabled)?.length || 0;
  const totalChannels = channels?.data?.length || 0;
  const totalMovies = movies?.data?.length || 0;
  const currentConnections = liveConnections?.data?.length || 0;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Real-time overview of your XUI API system</p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Users"
          value={totalUsers}
          icon={Users}
          color="blue"
          subtitle={`${activeUsers} active`}
          trend={{ value: 12, isPositive: true }}
        />
        <MetricCard
          title="Live Connections"
          value={currentConnections}
          icon={Activity}
          color="green"
          subtitle="Currently watching"
          isRealtime
        />
        <MetricCard
          title="Streams"
          value={totalStreams}
          icon={Tv}
          color="purple"
          subtitle={`${activeStreams} active`}
        />
        <MetricCard
          title="Content"
          value={totalChannels + totalMovies}
          icon={Film}
          color="orange"
          subtitle={`${totalChannels} channels, ${totalMovies} movies`}
        />
      </div>

      {/* Server Stats */}
      {serverStats?.data && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="CPU Usage"
            value={`${serverStats.data.cpu_usage || 0}%`}
            icon={Server}
            color={serverStats.data.cpu_usage > 80 ? 'red' : 'blue'}
          />
          <MetricCard
            title="RAM Usage"
            value={`${serverStats.data.ram_usage || 0}%`}
            icon={Server}
            color={serverStats.data.ram_usage > 80 ? 'red' : 'blue'}
          />
          <MetricCard
            title="Network Up"
            value={`${(serverStats.data.network_speed_up || 0).toFixed(1)} MB/s`}
            icon={Upload}
            color="green"
          />
          <MetricCard
            title="Network Down"
            value={`${(serverStats.data.network_speed_down || 0).toFixed(1)} MB/s`}
            icon={Download}
            color="blue"
          />
        </div>
      )}

      {/* Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Connections Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Live Connections</h3>
            <p className="text-sm text-gray-500">Real-time connection monitoring</p>
          </div>
          <div className="card-body">
            <RealtimeChart
              data={liveConnections?.data || []}
              isLoading={connectionsLoading}
            />
          </div>
        </div>

        {/* Activity Feed */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
            <p className="text-sm text-gray-500">Latest system events</p>
          </div>
          <div className="card-body">
            <ActivityFeed
              activities={activityLogs?.data || []}
              isLoading={logsLoading}
            />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="btn-primary">
              <Users className="h-4 w-4 mr-2" />
              Add New User
            </button>
            <button className="btn-secondary">
              <Tv className="h-4 w-4 mr-2" />
              Create Stream
            </button>
            <button className="btn-secondary">
              <Eye className="h-4 w-4 mr-2" />
              View All Logs
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
