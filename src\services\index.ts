// Export all services
export { XUIApiClient, apiClient, defaultApiConfig } from './api-client';
export { UserService, LineService, MagService, EnigmaService } from './user-service';
export { StreamService, ChannelService, RadioService, MovieService, SeriesService } from './content-service';
export { LogsService, MonitoringService, SecurityService } from './monitoring-service';

// Create service instances
import { apiClient } from './api-client';
import { UserService, LineService, MagService, EnigmaService } from './user-service';
import { StreamService, ChannelService, RadioService, MovieService, SeriesService } from './content-service';
import { LogsService, MonitoringService, SecurityService } from './monitoring-service';

// Service instances
export const userService = new UserService(apiClient);
export const lineService = new LineService(apiClient);
export const magService = new MagService(apiClient);
export const enigmaService = new EnigmaService(apiClient);
export const streamService = new StreamService(apiClient);
export const channelService = new ChannelService(apiClient);
export const radioService = new RadioService(apiClient);
export const movieService = new MovieService(apiClient);
export const seriesService = new SeriesService(apiClient);
export const logsService = new LogsService(apiClient);
export const monitoringService = new MonitoringService(apiClient);
export const securityService = new SecurityService(apiClient);

// Additional services for configuration management
export class ConfigService {
  constructor(private apiClient: typeof apiClient) {}

  // Get system settings
  async getSettings() {
    return this.apiClient.makeRequest('get_settings');
  }

  // Edit system settings
  async editSettings(settings: any) {
    return this.apiClient.makeRequest('edit_settings', settings);
  }

  // Get categories
  async getCategories() {
    return this.apiClient.makeRequest('get_categories');
  }

  // Get specific category
  async getCategory(id: number) {
    return this.apiClient.makeRequest('get_category', { id });
  }

  // Create category
  async createCategory(categoryData: any) {
    return this.apiClient.makeRequest('create_category', categoryData);
  }

  // Edit category
  async editCategory(id: number, categoryData: any) {
    return this.apiClient.makeRequest('edit_category', { id, ...categoryData });
  }

  // Delete category
  async deleteCategory(id: number) {
    return this.apiClient.makeRequest('delete_category', { id });
  }

  // Get bouquets
  async getBouquets() {
    return this.apiClient.makeRequest('get_bouquets');
  }

  // Get specific bouquet
  async getBouquet(id: number) {
    return this.apiClient.makeRequest('get_bouquet', { id });
  }

  // Create bouquet
  async createBouquet(bouquetData: any) {
    return this.apiClient.makeRequest('create_bouquet', bouquetData);
  }

  // Edit bouquet
  async editBouquet(id: number, bouquetData: any) {
    return this.apiClient.makeRequest('edit_bouquet', { id, ...bouquetData });
  }

  // Delete bouquet
  async deleteBouquet(id: number) {
    return this.apiClient.makeRequest('delete_bouquet', { id });
  }

  // Get packages
  async getPackages() {
    return this.apiClient.makeRequest('get_packages');
  }

  // Get specific package
  async getPackage(id: number) {
    return this.apiClient.makeRequest('get_package', { id });
  }

  // Create package
  async createPackage(packageData: any) {
    return this.apiClient.makeRequest('create_package', packageData);
  }

  // Edit package
  async editPackage(id: number, packageData: any) {
    return this.apiClient.makeRequest('edit_package', { id, ...packageData });
  }

  // Delete package
  async deletePackage(id: number) {
    return this.apiClient.makeRequest('delete_package', { id });
  }

  // Get groups
  async getGroups() {
    return this.apiClient.makeRequest('get_groups');
  }

  // Get specific group
  async getGroup(id: number) {
    return this.apiClient.makeRequest('get_group', { id });
  }

  // Create group
  async createGroup(groupData: any) {
    return this.apiClient.makeRequest('create_group', groupData);
  }

  // Edit group
  async editGroup(id: number, groupData: any) {
    return this.apiClient.makeRequest('edit_group', { id, ...groupData });
  }

  // Delete group
  async deleteGroup(id: number) {
    return this.apiClient.makeRequest('delete_group', { id });
  }

  // Get servers
  async getServers() {
    return this.apiClient.makeRequest('get_servers');
  }

  // Get specific server
  async getServer(id: number) {
    return this.apiClient.makeRequest('get_server', { id });
  }

  // Install server
  async installServer(serverData: any) {
    return this.apiClient.makeRequest('install_server', serverData);
  }

  // Edit server
  async editServer(id: number, serverData: any) {
    return this.apiClient.makeRequest('edit_server', { id, ...serverData });
  }

  // Delete server
  async deleteServer(id: number) {
    return this.apiClient.makeRequest('delete_server', { id });
  }

  // MySQL query (admin function)
  async mysqlQuery(query: string) {
    return this.apiClient.makeRequest('mysql_query', { query });
  }
}

export const configService = new ConfigService(apiClient);
