import { XUIApiClient } from './api-client';
import { Stream, Channel, Movie, Series, Episode, ApiResponse } from '@/types/api';

export class StreamService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all streams
  async getStreams(): Promise<ApiResponse<Stream[]>> {
    return this.apiClient.makeRequest<Stream[]>('get_streams');
  }

  // Get specific stream by ID
  async getStream(id: number): Promise<ApiResponse<Stream>> {
    return this.apiClient.makeRequest<Stream>('get_stream', { id });
  }

  // Create new stream
  async createStream(streamData: Partial<Stream>): Promise<ApiResponse<Stream>> {
    return this.apiClient.makeRequest<Stream>('create_stream', streamData);
  }

  // Edit existing stream
  async editStream(id: number, streamData: Partial<Stream>): Promise<ApiResponse<Stream>> {
    return this.apiClient.makeRequest<Stream>('edit_stream', { id, ...streamData });
  }

  // Delete stream
  async deleteStream(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_stream', { id });
  }

  // Start stream
  async startStream(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('start_stream', params);
  }

  // Stop stream
  async stopStream(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('stop_stream', params);
  }
}

export class ChannelService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all channels
  async getChannels(): Promise<ApiResponse<Channel[]>> {
    return this.apiClient.makeRequest<Channel[]>('get_channels');
  }

  // Get specific channel by ID
  async getChannel(id: number): Promise<ApiResponse<Channel>> {
    return this.apiClient.makeRequest<Channel>('get_channel', { id });
  }

  // Create new channel
  async createChannel(channelData: Partial<Channel>): Promise<ApiResponse<Channel>> {
    return this.apiClient.makeRequest<Channel>('create_channel', channelData);
  }

  // Edit existing channel
  async editChannel(id: number, channelData: Partial<Channel>): Promise<ApiResponse<Channel>> {
    return this.apiClient.makeRequest<Channel>('edit_channel', { id, ...channelData });
  }

  // Delete channel
  async deleteChannel(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_channel', { id });
  }

  // Start channel
  async startChannel(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('start_channel', params);
  }

  // Stop channel
  async stopChannel(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('stop_channel', params);
  }
}

export class RadioService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all radio stations
  async getStations(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_stations');
  }

  // Get specific radio station by ID
  async getStation(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_station', { id });
  }

  // Create new radio station
  async createStation(stationData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('create_station', stationData);
  }

  // Edit existing radio station
  async editStation(id: number, stationData: any): Promise<ApiResponse> {
    return this.apiClient.makeRequest('edit_station', { id, ...stationData });
  }

  // Delete radio station
  async deleteStation(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_station', { id });
  }

  // Start radio station
  async startStation(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('start_station', params);
  }

  // Stop radio station
  async stopStation(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('stop_station', params);
  }
}

export class MovieService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all movies
  async getMovies(): Promise<ApiResponse<Movie[]>> {
    return this.apiClient.makeRequest<Movie[]>('get_movies');
  }

  // Get specific movie by ID
  async getMovie(id: number): Promise<ApiResponse<Movie>> {
    return this.apiClient.makeRequest<Movie>('get_movie', { id });
  }

  // Create new movie
  async createMovie(movieData: Partial<Movie>): Promise<ApiResponse<Movie>> {
    return this.apiClient.makeRequest<Movie>('create_movie', movieData);
  }

  // Edit existing movie
  async editMovie(id: number, movieData: Partial<Movie>): Promise<ApiResponse<Movie>> {
    return this.apiClient.makeRequest<Movie>('edit_movie', { id, ...movieData });
  }

  // Delete movie
  async deleteMovie(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_movie', { id });
  }

  // Start movie
  async startMovie(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('start_movie', params);
  }

  // Stop movie
  async stopMovie(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('stop_movie', params);
  }
}

export class SeriesService {
  constructor(private apiClient: XUIApiClient) {}

  // Get all series
  async getSeriesList(): Promise<ApiResponse<Series[]>> {
    return this.apiClient.makeRequest<Series[]>('get_series_list');
  }

  // Get specific series by ID
  async getSeries(id: number): Promise<ApiResponse<Series>> {
    return this.apiClient.makeRequest<Series>('get_series', { id });
  }

  // Create new series
  async createSeries(seriesData: Partial<Series>): Promise<ApiResponse<Series>> {
    return this.apiClient.makeRequest<Series>('create_series', seriesData);
  }

  // Edit existing series
  async editSeries(id: number, seriesData: Partial<Series>): Promise<ApiResponse<Series>> {
    return this.apiClient.makeRequest<Series>('edit_series', { id, ...seriesData });
  }

  // Delete series
  async deleteSeries(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_series', { id });
  }

  // Get all episodes
  async getEpisodes(seriesId?: number): Promise<ApiResponse<Episode[]>> {
    const params = seriesId ? { series_id: seriesId } : {};
    return this.apiClient.makeRequest<Episode[]>('get_episodes', params);
  }

  // Get specific episode by ID
  async getEpisode(id: number): Promise<ApiResponse<Episode>> {
    return this.apiClient.makeRequest<Episode>('get_episode', { id });
  }

  // Create new episode
  async createEpisode(episodeData: Partial<Episode>): Promise<ApiResponse<Episode>> {
    return this.apiClient.makeRequest<Episode>('create_episode', episodeData);
  }

  // Edit existing episode
  async editEpisode(id: number, episodeData: Partial<Episode>): Promise<ApiResponse<Episode>> {
    return this.apiClient.makeRequest<Episode>('edit_episode', { id, ...episodeData });
  }

  // Delete episode
  async deleteEpisode(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_episode', { id });
  }

  // Start episode
  async startEpisode(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('start_episode', params);
  }

  // Stop episode
  async stopEpisode(id: number, serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { id, server_id: serverId } : { id };
    return this.apiClient.makeRequest('stop_episode', params);
  }
}
