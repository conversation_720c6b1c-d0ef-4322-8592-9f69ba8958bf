# XUI API Manager

A comprehensive real-time management application for XUI API systems, providing full control over IPTV streaming services, user management, content administration, and system monitoring.

## 🚀 Features

### Core Functionality
- **Real-time Dashboard** - Live monitoring of connections, server stats, and system health
- **User Management** - Complete CRUD operations for users, lines, MAG devices, and Enigma devices
- **Content Management** - Manage streams, channels, movies, series, and episodes
- **Live Monitoring** - Real-time connection tracking, activity logs, and system metrics
- **Security Management** - IP blocking, user agent filtering, and access control
- **Server Administration** - Multi-server management and configuration

### API Coverage
The application implements all XUI API endpoints including:

#### User & Device Management
- Users, Lines, MAG Devices, Enigma Devices
- Enable/disable, ban/unban operations
- Bulk operations and filtering

#### Content Management
- Streams, Channels, Radio Stations
- Movies, Series, Episodes
- Start/stop operations
- Category and bouquet management

#### Monitoring & Logs
- Activity logs, Live connections
- Credit logs, Client logs, User logs
- Stream errors, System logs
- Login logs, Restream logs, MAG events

#### System Administration
- Server statistics and monitoring
- Settings configuration
- Security management
- Database operations

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript
- **State Management**: TanStack Query (React Query)
- **Routing**: React Router v6
- **Styling**: Tailwind CSS
- **Charts**: Recharts
- **HTTP Client**: Axios
- **Build Tool**: Vite
- **Icons**: Lucide React

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm

### Setup Instructions

1. **Clone or download the project files**
   ```bash
   # If you have the files, navigate to the project directory
   cd xui-api-manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Configure API settings**
   
   Edit `src/services/api-client.ts` to update your API configuration:
   ```typescript
   export const defaultApiConfig: ApiConfig = {
     baseUrl: 'http://your-server-ip',
     apiKey: 'your-api-key',
     accessCode: 'your-access-code',
   };
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### API Configuration
The main API configuration is located in `src/services/api-client.ts`. Update the following values:

```typescript
export const defaultApiConfig: ApiConfig = {
  baseUrl: 'http://**************',        // Your XUI server URL
  apiKey: '1AA7A7903032096A174ED700B1048EDC', // Your API key
  accessCode: '5m3gVTKM',                   // Your access code
};
```

### Environment Variables (Optional)
Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=http://your-server-ip
VITE_API_KEY=your-api-key
VITE_ACCESS_CODE=your-access-code
```

## 🎯 Usage

### Dashboard
- View real-time system metrics
- Monitor active connections
- Track server performance
- Quick access to common actions

### User Management
- **Users Tab**: Manage regular users
- **Lines Tab**: Manage line accounts
- **MAG Devices**: Manage MAG device connections
- **Enigma Devices**: Manage Enigma device connections

### Content Management
- **Streams**: Create and manage streaming sources
- **Channels**: Manage live TV channels
- **Movies**: Organize movie content
- **Series**: Manage TV series and episodes

### Monitoring
- **Live Connections**: Real-time connection monitoring
- **Activity Logs**: System activity tracking
- **Server Stats**: Performance metrics
- **Error Logs**: System error tracking

## 🔄 Real-time Features

The application includes several real-time monitoring capabilities:

- **Live Connection Tracking** (updates every 5 seconds)
- **Server Statistics** (updates every 10 seconds)
- **Activity Logs** (updates every 15 seconds)
- **General Data Refresh** (updates every 30 seconds)

## 🏗️ Build for Production

```bash
npm run build
# or
yarn build
# or
pnpm build
```

The built files will be in the `dist` directory, ready for deployment to any static hosting service.

## 🔒 Security Considerations

- Always use HTTPS in production
- Keep your API keys secure and never commit them to version control
- Implement proper authentication and authorization
- Regularly update dependencies
- Use environment variables for sensitive configuration

## 🤝 API Endpoints Supported

The application supports all major XUI API endpoints:

### Information & Stats
- `get_settings`, `get_server_stats`, `user_info`
- `get_lines`, `get_mags`, `get_enigmas`, `get_users`
- `get_streams`, `get_channels`, `get_stations`, `get_movies`, `get_series_list`

### User Management
- `create_user`, `edit_user`, `delete_user`, `enable_user`, `disable_user`
- `create_line`, `edit_line`, `delete_line`, `enable_line`, `disable_line`
- `create_mag`, `edit_mag`, `delete_mag`, `enable_mag`, `disable_mag`

### Content Management
- `create_stream`, `edit_stream`, `delete_stream`, `start_stream`, `stop_stream`
- `create_channel`, `edit_channel`, `delete_channel`, `start_channel`, `stop_channel`
- `create_movie`, `edit_movie`, `delete_movie`

### Monitoring & Logs
- `activity_logs`, `live_connections`, `credit_logs`, `client_logs`
- `stream_errors`, `system_logs`, `login_logs`

### System Administration
- `reload_nginx`, `clear_temp`, `clear_streams`, `update_blacklist`
- `kill_pid`, `kill_connection`, `get_free_space`

## 📝 License

MIT License - feel free to use this project for your XUI API management needs.

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Verify your API URL, key, and access code
   - Check if the XUI server is accessible
   - Ensure CORS is properly configured

2. **Build Errors**
   - Clear node_modules and reinstall dependencies
   - Check Node.js version compatibility

3. **Real-time Updates Not Working**
   - Check network connectivity
   - Verify API endpoints are responding
   - Check browser console for errors

For more help, check the browser console for detailed error messages.
