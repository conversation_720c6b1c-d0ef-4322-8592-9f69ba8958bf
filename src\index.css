@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-active {
    @apply status-badge bg-green-100 text-green-800;
  }
  
  .status-inactive {
    @apply status-badge bg-red-100 text-red-800;
  }
  
  .status-pending {
    @apply status-badge bg-yellow-100 text-yellow-800;
  }
  
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out;
  }
  
  .sidebar-open {
    @apply translate-x-0;
  }
  
  .sidebar-closed {
    @apply -translate-x-full;
  }
  
  .nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors;
  }
  
  .nav-item-active {
    @apply nav-item bg-primary-50 text-primary-700 border-r-2 border-primary-600;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }
  
  .metric-card {
    @apply card p-6;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-gray-900;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-500;
  }
  
  .metric-change {
    @apply text-sm font-medium;
  }
  
  .metric-change-positive {
    @apply metric-change text-green-600;
  }
  
  .metric-change-negative {
    @apply metric-change text-red-600;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
