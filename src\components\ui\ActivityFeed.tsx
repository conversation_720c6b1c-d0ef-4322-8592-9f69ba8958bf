import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { User, Tv, Play, Pause, AlertCircle } from 'lucide-react';
import LoadingSpinner from './LoadingSpinner';

interface Activity {
  id: number;
  user_id: number;
  stream_id?: number;
  user_agent?: string;
  user_ip?: string;
  date: string;
  container?: string;
  action?: string;
}

interface ActivityFeedProps {
  activities: Activity[];
  isLoading?: boolean;
}

const getActivityIcon = (activity: Activity) => {
  if (activity.stream_id) {
    return <Tv className="h-4 w-4 text-blue-500" />;
  }
  return <User className="h-4 w-4 text-gray-500" />;
};

const getActivityDescription = (activity: Activity) => {
  if (activity.stream_id) {
    return `User ${activity.user_id} started watching stream ${activity.stream_id}`;
  }
  return `User ${activity.user_id} performed an action`;
};

const ActivityFeed: React.FC<ActivityFeedProps> = ({ activities, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <LoadingSpinner />
      </div>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500">No recent activity</p>
      </div>
    );
  }

  return (
    <div className="space-y-4 max-h-80 overflow-y-auto">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start space-x-3">
          <div className="flex-shrink-0 mt-1">
            {getActivityIcon(activity)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm text-gray-900">
              {getActivityDescription(activity)}
            </p>
            <div className="mt-1 flex items-center space-x-2 text-xs text-gray-500">
              <span>
                {formatDistanceToNow(new Date(activity.date), { addSuffix: true })}
              </span>
              {activity.user_ip && (
                <>
                  <span>•</span>
                  <span>{activity.user_ip}</span>
                </>
              )}
              {activity.container && (
                <>
                  <span>•</span>
                  <span className="uppercase">{activity.container}</span>
                </>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ActivityFeed;
