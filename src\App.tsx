import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';

function App() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🚀 XUI API Manager
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Real-time IPTV Management Application
        </p>
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md">
          <h2 className="text-2xl font-semibold text-green-600 mb-4">
            ✅ Application Running Successfully!
          </h2>
          <div className="space-y-4 text-left">
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>React + TypeScript</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Vite Development Server</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Tailwind CSS</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>XUI API Integration Ready</span>
            </div>
          </div>
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>API Endpoint:</strong><br/>
              http://**************/5m3gVTKM
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
