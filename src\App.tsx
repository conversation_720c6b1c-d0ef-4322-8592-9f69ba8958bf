import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Menu, X, Activity, Users, Tv, Radio, Film, Settings, Shield, Server, BarChart3 } from 'lucide-react';

// Simple placeholder components for now
const Dashboard = () => (
  <div className="space-y-6">
    <div>
      <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
      <p className="text-gray-600">Real-time overview of your XUI API system</p>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <Users className="h-8 w-8 text-blue-500" />
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500">Total Users</p>
            <p className="text-2xl font-bold text-gray-900">1,234</p>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <Activity className="h-8 w-8 text-green-500" />
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500">Live Connections</p>
            <p className="text-2xl font-bold text-gray-900">567</p>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <Tv className="h-8 w-8 text-purple-500" />
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500">Streams</p>
            <p className="text-2xl font-bold text-gray-900">89</p>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <Film className="h-8 w-8 text-orange-500" />
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500">Content</p>
            <p className="text-2xl font-bold text-gray-900">2,456</p>
          </div>
        </div>
      </div>
    </div>
  </div>
);

const UsersPage = () => (
  <div className="space-y-6">
    <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
    <div className="bg-white rounded-lg shadow p-6">
      <p className="text-gray-600">User management interface</p>
    </div>
  </div>
);

const StreamsPage = () => (
  <div className="space-y-6">
    <h1 className="text-2xl font-bold text-gray-900">Streams</h1>
    <div className="bg-white rounded-lg shadow p-6">
      <p className="text-gray-600">Stream management interface</p>
    </div>
  </div>
);

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0`}>
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">XUI</span>
                </div>
              </div>
              <div className="ml-3">
                <h1 className="text-lg font-semibold text-gray-900">API Manager</h1>
                <p className="text-xs text-gray-500">Real-time Control</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 px-2 py-4">
            <a href="/" className="flex items-center px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 rounded-md">
              <BarChart3 className="mr-3 h-5 w-5" />
              Dashboard
            </a>
            <a href="/users" className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md">
              <Users className="mr-3 h-5 w-5" />
              Users
            </a>
            <a href="/streams" className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md">
              <Tv className="mr-3 h-5 w-5" />
              Streams
            </a>
            <a href="/monitoring" className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md">
              <Activity className="mr-3 h-5 w-5" />
              Monitoring
            </a>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              >
                <Menu className="h-6 w-6" />
              </button>
              <div className="hidden lg:block">
                <h1 className="text-xl font-semibold text-gray-900">
                  XUI API Management Dashboard
                </h1>
                <p className="text-sm text-gray-500">
                  Real-time IPTV streaming control and monitoring
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-green-600">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-xs font-medium">Connected</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/users" element={<UsersPage />} />
              <Route path="/streams" element={<StreamsPage />} />
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
