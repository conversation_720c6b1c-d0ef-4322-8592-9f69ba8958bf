import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Menu, X, Activity, Users, Tv, Radio, Film, Settings, Shield, Server, BarChart3 } from 'lucide-react';
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import Dashboard from './components/pages/Dashboard';
import UsersPage from './components/pages/UsersPage';
import StreamsPage from './components/pages/StreamsPage';
import ChannelsPage from './components/pages/ChannelsPage';
import MoviesPage from './components/pages/MoviesPage';
import SeriesPage from './components/pages/SeriesPage';
import MonitoringPage from './components/pages/MonitoringPage';
import SettingsPage from './components/pages/SettingsPage';
import { NavigationItem } from './types/navigation';

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    href: '/',
    icon: BarChart3,
  },
  {
    id: 'users',
    name: 'User Management',
    href: '/users',
    icon: Users,
    children: [
      { id: 'lines', name: 'Lines', href: '/users/lines' },
      { id: 'mag', name: 'MAG Devices', href: '/users/mag' },
      { id: 'enigma', name: 'Enigma Devices', href: '/users/enigma' },
    ],
  },
  {
    id: 'content',
    name: 'Content Management',
    href: '/content',
    icon: Tv,
    children: [
      { id: 'streams', name: 'Streams', href: '/content/streams' },
      { id: 'channels', name: 'Live TV', href: '/content/channels' },
      { id: 'radio', name: 'Radio', href: '/content/radio' },
      { id: 'movies', name: 'Movies', href: '/content/movies' },
      { id: 'series', name: 'Series', href: '/content/series' },
    ],
  },
  {
    id: 'monitoring',
    name: 'Monitoring',
    href: '/monitoring',
    icon: Activity,
    children: [
      { id: 'logs', name: 'Logs', href: '/monitoring/logs' },
      { id: 'connections', name: 'Live Connections', href: '/monitoring/connections' },
      { id: 'stats', name: 'Server Stats', href: '/monitoring/stats' },
    ],
  },
  {
    id: 'security',
    name: 'Security',
    href: '/security',
    icon: Shield,
  },
  {
    id: 'servers',
    name: 'Servers',
    href: '/servers',
    icon: Server,
  },
  {
    id: 'settings',
    name: 'Settings',
    href: '/settings',
    icon: Settings,
  },
];

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <Sidebar
        navigationItems={navigationItems}
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/users/*" element={<UsersPage />} />
              <Route path="/content/streams" element={<StreamsPage />} />
              <Route path="/content/channels" element={<ChannelsPage />} />
              <Route path="/content/movies" element={<MoviesPage />} />
              <Route path="/content/series" element={<SeriesPage />} />
              <Route path="/monitoring/*" element={<MonitoringPage />} />
              <Route path="/settings" element={<SettingsPage />} />
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
