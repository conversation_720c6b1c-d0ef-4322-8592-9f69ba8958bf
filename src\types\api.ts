// Base API Configuration
export interface ApiConfig {
  baseUrl: string;
  apiKey: string;
  accessCode: string;
}

// Common API Response
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// User Management Types
export interface User {
  id: number;
  username: string;
  password: string;
  email?: string;
  enabled: boolean;
  admin_enabled: boolean;
  exp_date: string;
  registered_date: string;
  max_connections: number;
  is_trial: boolean;
  active_cons: number;
  created_by: string;
  bouquets_selected: number[];
  allowed_ips?: string[];
  allowed_ua?: string[];
}

export interface CreateUserRequest {
  username: string;
  password: string;
  email?: string;
  enabled?: boolean;
  exp_date?: string;
  max_connections?: number;
  is_trial?: boolean;
  bouquets_selected?: number[];
  allowed_ips?: string[];
  allowed_ua?: string[];
}

// Line Management Types
export interface Line {
  id: number;
  username: string;
  password: string;
  enabled: boolean;
  admin_enabled: boolean;
  exp_date: string;
  registered_date: string;
  max_connections: number;
  is_trial: boolean;
  active_cons: number;
  created_by: string;
  bouquets_selected: number[];
  allowed_ips?: string[];
  allowed_ua?: string[];
  is_restreamer: boolean;
  restreamer_dns?: string;
}

// MAG Device Types
export interface MagDevice {
  id: number;
  mag_id: string;
  enabled: boolean;
  exp_date: string;
  registered_date: string;
  last_activity: string;
  bouquets_selected: number[];
  created_by: string;
}

// Enigma Device Types
export interface EnigmaDevice {
  id: number;
  enigma_id: string;
  enabled: boolean;
  exp_date: string;
  registered_date: string;
  last_activity: string;
  bouquets_selected: number[];
  created_by: string;
}

// Stream Types
export interface Stream {
  id: number;
  stream_display_name: string;
  stream_source: string;
  stream_icon?: string;
  category_id: number;
  enabled: boolean;
  created_channel_location?: string;
  transcode_profile_id?: number;
  custom_ffmpeg?: string;
  notes?: string;
  probesize_ondemand?: number;
  custom_map?: string;
  external_push?: string;
}

// Channel Types
export interface Channel {
  id: number;
  channel_name: string;
  channel_id: string;
  epg_id?: string;
  category_id: number;
  enabled: boolean;
  stream_source: string;
  stream_icon?: string;
  transcode_profile_id?: number;
  custom_ffmpeg?: string;
  notes?: string;
}

// Movie Types
export interface Movie {
  id: number;
  name: string;
  stream_id: number;
  added: string;
  category_id: number;
  container_extension: string;
  custom_sid?: string;
  direct_source?: string;
}

// Series Types
export interface Series {
  id: number;
  name: string;
  category_id: number;
  cover?: string;
  plot?: string;
  cast?: string;
  director?: string;
  genre?: string;
  release_date?: string;
  last_modified: string;
}

export interface Episode {
  id: number;
  series_id: number;
  season_num: number;
  episode_num: number;
  title: string;
  container_extension: string;
  info?: {
    plot?: string;
    duration?: string;
    movie_image?: string;
  };
  custom_sid?: string;
  direct_source?: string;
}

// Category Types
export interface Category {
  category_id: number;
  category_name: string;
  category_type: 'live' | 'movie' | 'series' | 'radio';
  parent_id?: number;
}

// Bouquet Types
export interface Bouquet {
  id: number;
  bouquet_name: string;
  bouquet_channels: number[];
  bouquet_series: number[];
  bouquet_movies: number[];
}

// Package Types
export interface Package {
  id: number;
  package_name: string;
  bouquets: number[];
  is_trial: boolean;
  trial_duration: number;
  official_duration: number;
  max_connections: number;
}

// Server Types
export interface Server {
  id: number;
  server_name: string;
  domain_name: string;
  server_ip: string;
  vpn_ip?: string;
  ssh_password: string;
  ssh_port: number;
  diff_time_main: number;
  http_broadcast_port: number;
  total_clients: number;
  system_os: string;
  network_interface: string;
  latency: number;
  status: number;
  enabled: boolean;
}

// Logs and Events Types
export interface ActivityLog {
  id: number;
  user_id: number;
  stream_id: number;
  user_agent: string;
  user_ip: string;
  date: string;
  container: string;
}

export interface LiveConnection {
  id: number;
  user_id: number;
  stream_id: number;
  server_id: number;
  user_agent: string;
  user_ip: string;
  date_start: string;
  date_end?: string;
  container: string;
  pid?: number;
}

// Statistics Types
export interface ServerStats {
  cpu_usage: number;
  ram_usage: number;
  disk_usage: number;
  network_speed_up: number;
  network_speed_down: number;
  total_connections: number;
  bytes_sent: number;
  bytes_received: number;
}

// Settings Types
export interface Settings {
  server_name: string;
  allow_countries: string[];
  block_countries: string[];
  block_ips: string[];
  block_user_agents: string[];
  max_connections_per_ip: number;
  flood_seconds_control: number;
  flood_max_requests: number;
  client_area_access: boolean;
  stalker_access: boolean;
  enigma_access: boolean;
  mag_access: boolean;
  xmltv_access: boolean;
}
