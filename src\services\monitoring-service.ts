import { XUIApiClient } from './api-client';
import { ActivityLog, LiveConnection, ServerStats, ApiResponse } from '@/types/api';

export class LogsService {
  constructor(private apiClient: XUIApiClient) {}

  // Get activity logs
  async getActivityLogs(params?: {
    user_id?: number;
    stream_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<ActivityLog[]>> {
    return this.apiClient.makeRequest<ActivityLog[]>('activity_logs', params);
  }

  // Get live connections
  async getLiveConnections(serverId?: number): Promise<ApiResponse<LiveConnection[]>> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest<LiveConnection[]>('live_connections', params);
  }

  // Get credit logs
  async getCreditLogs(params?: {
    user_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('credit_logs', params);
  }

  // Get client logs
  async getClientLogs(params?: {
    user_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('client_logs', params);
  }

  // Get user logs
  async getUserLogs(params?: {
    user_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('user_logs', params);
  }

  // Get stream errors
  async getStreamErrors(params?: {
    stream_id?: number;
    server_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('stream_errors', params);
  }

  // Watch output
  async watchOutput(params: {
    stream_id: number;
    server_id?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('watch_output', params);
  }

  // Get system logs
  async getSystemLogs(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('system_logs', params);
  }

  // Get login logs
  async getLoginLogs(params?: {
    user_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('login_logs', params);
  }

  // Get restream logs
  async getRestreamLogs(params?: {
    stream_id?: number;
    server_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('restream_logs', params);
  }

  // Get MAG events
  async getMagEvents(params?: {
    mag_id?: string;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('mag_events', params);
  }
}

export class MonitoringService {
  constructor(private apiClient: XUIApiClient) {}

  // Get server statistics
  async getServerStats(serverId?: number): Promise<ApiResponse<ServerStats>> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest<ServerStats>('get_server_stats', params);
  }

  // Get FPM status
  async getFpmStatus(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('get_fpm_status', params);
  }

  // Get RTMP statistics
  async getRtmpStats(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('get_rtmp_stats', params);
  }

  // Get free space
  async getFreeSpace(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('get_free_space', params);
  }

  // Get running processes (PIDs)
  async getPids(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('get_pids', params);
  }

  // Get certificate information
  async getCertificateInfo(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('get_certificate_info', params);
  }

  // Get directory listing
  async getDirectory(params: {
    path: string;
    server_id?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_directory', params);
  }

  // Kill process by PID
  async killPid(params: {
    pid: number;
    server_id?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('kill_pid', params);
  }

  // Kill connection
  async killConnection(params: {
    connection_id: number;
    server_id?: number;
  }): Promise<ApiResponse> {
    return this.apiClient.makeRequest('kill_connection', params);
  }

  // Reload Nginx
  async reloadNginx(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('reload_nginx', params);
  }

  // Clear temporary files
  async clearTemp(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('clear_temp', params);
  }

  // Clear streams
  async clearStreams(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('clear_streams', params);
  }

  // Update blacklist
  async updateBlacklist(serverId?: number): Promise<ApiResponse> {
    const params = serverId ? { server_id: serverId } : {};
    return this.apiClient.makeRequest('update_blacklist', params);
  }

  // Reload cache
  async reloadCache(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('reload_cache');
  }
}

export class SecurityService {
  constructor(private apiClient: XUIApiClient) {}

  // Get blocked ISPs
  async getBlockedIsps(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_blocked_isps');
  }

  // Add blocked ISP
  async addBlockedIsp(isp: string): Promise<ApiResponse> {
    return this.apiClient.makeRequest('add_blocked_isp', { isp });
  }

  // Delete blocked ISP
  async deleteBlockedIsp(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_blocked_isp', { id });
  }

  // Get blocked user agents
  async getBlockedUas(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_blocked_uas');
  }

  // Add blocked user agent
  async addBlockedUa(userAgent: string): Promise<ApiResponse> {
    return this.apiClient.makeRequest('add_blocked_ua', { user_agent: userAgent });
  }

  // Delete blocked user agent
  async deleteBlockedUa(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_blocked_ua', { id });
  }

  // Get blocked IPs
  async getBlockedIps(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('get_blocked_ips');
  }

  // Add blocked IP
  async addBlockedIp(ip: string): Promise<ApiResponse> {
    return this.apiClient.makeRequest('add_blocked_ip', { ip });
  }

  // Delete blocked IP
  async deleteBlockedIp(id: number): Promise<ApiResponse> {
    return this.apiClient.makeRequest('delete_blocked_ip', { id });
  }

  // Flush blocked IPs
  async flushBlockedIps(): Promise<ApiResponse> {
    return this.apiClient.makeRequest('flush_blocked_ips');
  }
}
