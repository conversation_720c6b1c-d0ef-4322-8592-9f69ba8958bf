{"name": "xui-api-manager", "version": "1.0.0", "description": "Real-time XUI API Management Application", "main": "dist/index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "axios": "^1.3.0", "socket.io-client": "^4.6.0", "@tanstack/react-query": "^4.24.0", "@tanstack/react-table": "^8.7.0", "react-hook-form": "^7.43.0", "react-hot-toast": "^2.4.0", "lucide-react": "^0.312.0", "clsx": "^1.2.1", "tailwind-merge": "^1.10.0", "recharts": "^2.5.0", "date-fns": "^2.29.0"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/node": "^18.14.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "eslint": "^8.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "jest": "^29.4.0", "postcss": "^8.4.21", "tailwindcss": "^3.2.6", "typescript": "^4.9.3", "vite": "^4.1.0"}, "keywords": ["xui", "api", "iptv", "streaming", "management", "real-time"], "author": "XUI API Manager", "license": "MIT"}